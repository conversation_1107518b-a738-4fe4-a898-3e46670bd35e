import type { SeoData } from '@components/SeoHelmet';
import { SeoHelmet } from '@components/SeoHelmet';
import { Typography } from '@components/typography';
import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useInfiniteDeals } from '@entities/deals/hooks/useInfiniteDeals';
import { useIsMobileView } from '@hooks/system';
import { useInfiniteScroll } from '@hooks/system/useInfiniteScroll';
import { getRouteApi } from '@tanstack/react-router';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import {
  getDealCategoryIcon,
  getDealCategoryTranslationKey,
} from '../../config';
import { isCategoryWithSeoData } from '../../utils';
import { DealsNoResult } from '../deals-no-results-section/DealsNoResults';
import { DealsList } from '../DealsList';

const routeApi = getRouteApi('/_protected/_main/deals');

export const DealsCategorySection = ({ className }: { className?: string }) => {
  const { title, category } = routeApi.useSearch();
  const isMobileView = useIsMobileView();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  // Use infinite deals hook for lazy loading
  const {
    data: deals,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    total,
    error,
  } = useInfiniteDeals({
    variables: {
      title,
      categories: category ? [category] : undefined,
    },
    limit: 20,
  });

  // Infinite scroll trigger
  const { triggerRef } = useInfiniteScroll(fetchNextPage, {
    enabled: !isLoading,
    hasNextPage,
    isFetching: isFetchingNextPage,
    rootMargin: '200px',
  });

  const seoData: SeoData = useMemo(() => {
    if (!isCategoryWithSeoData(category)) {
      return null;
    }

    const categoryData = LOCIZE_DEALS_KEYS.category[category];

    return {
      title: t(categoryData.meta.title),
      description: t(categoryData.meta.description),
      keywords: t(categoryData.meta.keywords),
    };
  }, [category, t]);

  const CategoryIcon = useMemo(() => {
    return getDealCategoryIcon(category || '');
  }, [category]);

  const mobileCategoryHeader = useMemo(() => {
    if (!isMobileView || !category) return null;

    const translationKey = getDealCategoryTranslationKey(category);
    const categoryName = t(translationKey);

    return (
      <div className="mb-6 flex items-center gap-2">
        <CategoryIcon className="h-5 w-5" />
        <Typography variant="xxs" affects="semibold">
          {categoryName}
        </Typography>
      </div>
    );
  }, [isMobileView, category, CategoryIcon]);

  // Handle error state
  if (error) {
    return (
      <>
        <SeoHelmet seoData={seoData} />
        <div className={className}>
          <div className="flex flex-col items-center justify-center py-12">
            <Typography variant="s" className="mb-4 text-center">
              {t('common.error', 'Something went wrong')}
            </Typography>
            <Typography
              variant="text-m"
              className="mb-6 text-center text-gray-600"
            >
              {t(
                'deals.errorMessage',
                'Failed to load deals. Please try again.',
              )}
            </Typography>
            <button
              onClick={() => window.location.reload()}
              className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
            >
              {t('common.retry', 'Try again')}
            </button>
          </div>
        </div>
      </>
    );
  }

  // Handle no results
  if (!isLoading && deals.length === 0) {
    return (
      <>
        <SeoHelmet seoData={seoData} />
        <div className={className}>
          <DealsNoResult />
        </div>
      </>
    );
  }

  return (
    <>
      <SeoHelmet seoData={seoData} />
      <div className={className}>
        {mobileCategoryHeader}

        {/* Show loading skeleton for initial load */}
        {isLoading ? (
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {Array.from({ length: 8 }, (_, index) => index).map((id) => (
              <div key={`skeleton-initial-${id}`} className="space-y-4">
                <Skeleton className="h-48 w-full rounded-lg" />
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            ))}
          </div>
        ) : (
          <>
            <DealsList data={deals} />

            {/* Infinite scroll trigger and loading indicator */}
            {hasNextPage && (
              <div
                ref={triggerRef as React.RefObject<HTMLDivElement>}
                className="mt-8 flex justify-center"
              >
                {isFetchingNextPage ? (
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                    {Array.from({ length: 4 }, (_, index) => index).map(
                      (id) => (
                        <div
                          key={`skeleton-loading-${id}`}
                          className="space-y-4"
                        >
                          <Skeleton className="h-48 w-full rounded-lg" />
                          <Skeleton className="h-4 w-3/4" />
                          <Skeleton className="h-4 w-1/2" />
                        </div>
                      ),
                    )}
                  </div>
                ) : (
                  <div className="text-center text-gray-500">
                    {t('common.loadMore', 'Load more')}
                  </div>
                )}
              </div>
            )}

            {/* Show total count */}
            {total > 0 && (
              <div className="mt-8 text-center text-sm text-gray-500">
                {t(
                  'deals.showingResults',
                  'Showing {{count}} of {{total}} deals',
                  {
                    count: deals.length,
                    total,
                  },
                )}
              </div>
            )}
          </>
        )}
      </div>
    </>
  );
};
