/** @generated THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */
import * as Types from '../../../shared/types/api.gen';

import {
  useQuery,
  useSuspenseQuery,
  UseQueryOptions,
  UseSuspenseQueryOptions,
} from '@tanstack/react-query';
import { fetcher } from '@lib/fetcher';
export type DealsQueryVariables = Types.Exact<{
  limit?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  featured?: Types.InputMaybe<Types.Scalars['Boolean']['input']>;
  page?: Types.InputMaybe<Types.Scalars['Int']['input']>;
  orderBy?: Types.InputMaybe<Types.DealsOrderBy>;
  direction?: Types.InputMaybe<Types.Direction>;
  title?: Types.InputMaybe<Types.Scalars['String']['input']>;
  categories?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.Scalars['String']['input']>>
    | Types.InputMaybe<Types.Scalars['String']['input']>
  >;
  merchant_name?: Types.InputMaybe<Types.Scalars['String']['input']>;
  statuses?: Types.InputMaybe<
    | Array<Types.InputMaybe<Types.DealStatus>>
    | Types.InputMaybe<Types.DealStatus>
  >;
}>;

export type DealsQuery = {
  deals?: {
    total: number;
    per_page: number;
    current_page: number;
    from?: number | null;
    to?: number | null;
    last_page: number;
    has_more_pages: boolean;
    data?: Array<{
      id: number;
      image_url?: string | null;
      image_path?: string | null;
      category_name: string;
      featured: boolean;
      merchant?: {
        name: string;
        deals_logo_url?: string | null;
        deals_logo_path?: string | null;
      } | null;
      translations?: Array<{
        id: number;
        language: string;
        deal_title: string;
        description: string;
        discount_label: string;
      } | null> | null;
    } | null> | null;
  } | null;
};

export type DealCategoriesQueryVariables = Types.Exact<{
  only_with_deals?: Types.InputMaybe<Types.Scalars['Boolean']['input']>;
}>;

export type DealCategoriesQuery = {
  deal_categories?: Array<{ name: string } | null> | null;
};

export type DealQueryVariables = Types.Exact<{
  dealId: Types.Scalars['Int']['input'];
}>;

export type DealQuery = {
  deal?: {
    id: number;
    image_url?: string | null;
    image_path?: string | null;
    category_name: string;
    promocode?: string | null;
    end_time?: string | null;
    merchant?: {
      name: string;
      deals_logo_url?: string | null;
      deals_logo_path?: string | null;
    } | null;
    translations?: Array<{
      id: number;
      language: string;
      deal_title: string;
      description: string;
      discount_label: string;
      tracking_url?: string | null;
    } | null> | null;
  } | null;
};

export const DealsDocument = `
    query Deals($limit: Int, $featured: Boolean, $page: Int, $orderBy: DealsOrderBy, $direction: Direction, $title: String, $categories: [String], $merchant_name: String, $statuses: [DealStatus]) {
  deals(
    limit: $limit
    featured: $featured
    page: $page
    orderBy: $orderBy
    direction: $direction
    title: $title
    categories: $categories
    merchant_name: $merchant_name
    statuses: $statuses
  ) {
    data {
      id
      image_url
      image_path
      category_name
      featured
      merchant {
        name
        deals_logo_url
        deals_logo_path
      }
      translations {
        id
        language
        deal_title
        description
        discount_label
      }
    }
    total
    per_page
    current_page
    from
    to
    last_page
    has_more_pages
  }
}
    `;

export const useDealsQuery = <TData = DealsQuery, TError = unknown>(
  variables?: DealsQueryVariables,
  options?: Omit<UseQueryOptions<DealsQuery, TError, TData>, 'queryKey'> & {
    queryKey?: UseQueryOptions<DealsQuery, TError, TData>['queryKey'];
  },
) => {
  return useQuery<DealsQuery, TError, TData>({
    queryKey: variables === undefined ? ['Deals'] : ['Deals', variables],
    queryFn: fetcher<DealsQuery, DealsQueryVariables>(DealsDocument, variables),
    ...options,
  });
};

useDealsQuery.getKey = (variables?: DealsQueryVariables) =>
  variables === undefined ? ['Deals'] : ['Deals', variables];

export const useSuspenseDealsQuery = <TData = DealsQuery, TError = unknown>(
  variables?: DealsQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<DealsQuery, TError, TData>,
    'queryKey'
  > & {
    queryKey?: UseSuspenseQueryOptions<DealsQuery, TError, TData>['queryKey'];
  },
) => {
  return useSuspenseQuery<DealsQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ['DealsSuspense']
        : ['DealsSuspense', variables],
    queryFn: fetcher<DealsQuery, DealsQueryVariables>(DealsDocument, variables),
    ...options,
  });
};

useSuspenseDealsQuery.getKey = (variables?: DealsQueryVariables) =>
  variables === undefined ? ['DealsSuspense'] : ['DealsSuspense', variables];

useDealsQuery.fetcher = (
  variables?: DealsQueryVariables,
  options?: RequestInit['headers'],
) =>
  fetcher<DealsQuery, DealsQueryVariables>(DealsDocument, variables, options);

export const DealCategoriesDocument = `
    query DealCategories($only_with_deals: Boolean) {
  deal_categories(only_with_deals: $only_with_deals) {
    name
  }
}
    `;

export const useDealCategoriesQuery = <
  TData = DealCategoriesQuery,
  TError = unknown,
>(
  variables?: DealCategoriesQueryVariables,
  options?: Omit<
    UseQueryOptions<DealCategoriesQuery, TError, TData>,
    'queryKey'
  > & {
    queryKey?: UseQueryOptions<DealCategoriesQuery, TError, TData>['queryKey'];
  },
) => {
  return useQuery<DealCategoriesQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ['DealCategories']
        : ['DealCategories', variables],
    queryFn: fetcher<DealCategoriesQuery, DealCategoriesQueryVariables>(
      DealCategoriesDocument,
      variables,
    ),
    ...options,
  });
};

useDealCategoriesQuery.getKey = (variables?: DealCategoriesQueryVariables) =>
  variables === undefined ? ['DealCategories'] : ['DealCategories', variables];

export const useSuspenseDealCategoriesQuery = <
  TData = DealCategoriesQuery,
  TError = unknown,
>(
  variables?: DealCategoriesQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<DealCategoriesQuery, TError, TData>,
    'queryKey'
  > & {
    queryKey?: UseSuspenseQueryOptions<
      DealCategoriesQuery,
      TError,
      TData
    >['queryKey'];
  },
) => {
  return useSuspenseQuery<DealCategoriesQuery, TError, TData>({
    queryKey:
      variables === undefined
        ? ['DealCategoriesSuspense']
        : ['DealCategoriesSuspense', variables],
    queryFn: fetcher<DealCategoriesQuery, DealCategoriesQueryVariables>(
      DealCategoriesDocument,
      variables,
    ),
    ...options,
  });
};

useSuspenseDealCategoriesQuery.getKey = (
  variables?: DealCategoriesQueryVariables,
) =>
  variables === undefined
    ? ['DealCategoriesSuspense']
    : ['DealCategoriesSuspense', variables];

useDealCategoriesQuery.fetcher = (
  variables?: DealCategoriesQueryVariables,
  options?: RequestInit['headers'],
) =>
  fetcher<DealCategoriesQuery, DealCategoriesQueryVariables>(
    DealCategoriesDocument,
    variables,
    options,
  );

export const DealDocument = `
    query Deal($dealId: Int!) {
  deal(deal_id: $dealId) {
    id
    image_url
    image_path
    category_name
    promocode
    merchant {
      name
      deals_logo_url
      deals_logo_path
    }
    translations {
      id
      language
      deal_title
      description
      discount_label
      tracking_url
    }
    end_time
  }
}
    `;

export const useDealQuery = <TData = DealQuery, TError = unknown>(
  variables: DealQueryVariables,
  options?: Omit<UseQueryOptions<DealQuery, TError, TData>, 'queryKey'> & {
    queryKey?: UseQueryOptions<DealQuery, TError, TData>['queryKey'];
  },
) => {
  return useQuery<DealQuery, TError, TData>({
    queryKey: ['Deal', variables],
    queryFn: fetcher<DealQuery, DealQueryVariables>(DealDocument, variables),
    ...options,
  });
};

useDealQuery.getKey = (variables: DealQueryVariables) => ['Deal', variables];

export const useSuspenseDealQuery = <TData = DealQuery, TError = unknown>(
  variables: DealQueryVariables,
  options?: Omit<
    UseSuspenseQueryOptions<DealQuery, TError, TData>,
    'queryKey'
  > & {
    queryKey?: UseSuspenseQueryOptions<DealQuery, TError, TData>['queryKey'];
  },
) => {
  return useSuspenseQuery<DealQuery, TError, TData>({
    queryKey: ['DealSuspense', variables],
    queryFn: fetcher<DealQuery, DealQueryVariables>(DealDocument, variables),
    ...options,
  });
};

useSuspenseDealQuery.getKey = (variables: DealQueryVariables) => [
  'DealSuspense',
  variables,
];

useDealQuery.fetcher = (
  variables: DealQueryVariables,
  options?: RequestInit['headers'],
) => fetcher<DealQuery, DealQueryVariables>(DealDocument, variables, options);
