query Deals(
  $limit: Int
  $featured: Boolean
  $page: Int
  $orderBy: DealsOrderBy
  $direction: Direction
  $title: String
  $categories: [String]
  $merchant_name: String
  $statuses: [DealStatus]
) {
  deals(
    limit: $limit
    featured: $featured
    page: $page
    orderBy: $orderBy
    direction: $direction
    title: $title
    categories: $categories
    merchant_name: $merchant_name
    statuses: $statuses
  ) {
    data {
      id
      image_url
      image_path
      category_name
      featured
      merchant {
        name
        deals_logo_url
        deals_logo_path
      }
      translations {
        id
        language
        deal_title
        description
        discount_label
      }
    }
    total
    per_page
    current_page
    from
    to
    last_page
    has_more_pages
  }
}

query DealCategories($only_with_deals: Boolean) {
  deal_categories(only_with_deals: $only_with_deals) {
    name
  }
}

query Deal($dealId: Int!) {
  deal(deal_id: $dealId) {
    id
    image_url
    image_path
    category_name
    promocode
    merchant {
      name
      deals_logo_url
      deals_logo_path
    }
    translations {
      id
      language
      deal_title
      description
      discount_label
      tracking_url
    }
    end_time
  }
}
