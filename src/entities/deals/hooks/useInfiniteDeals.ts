import { LANGUAGE_ABBREVIATION_BY_SHORTNAME } from '@entities/languages';
import type { Deal } from '@pages/deals/types';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { DealStatus } from '@/shared/types';

import { type DealsQueryVariables, useDealsQuery } from '../api/queries.gen';

export interface UseInfiniteDealsOptions {
  /**
   * Initial query variables for the deals query
   */
  variables?: Omit<DealsQueryVariables, 'page' | 'limit'>;
  /**
   * Number of items to load per page
   */
  limit?: number;
  /**
   * Whether to enable the query
   */
  enabled?: boolean;
}

export interface UseInfiniteDealsResult {
  /**
   * All loaded deals accumulated from all pages
   */
  data: Deal[];
  /**
   * Whether the initial query is loading
   */
  isLoading: boolean;
  /**
   * Whether a next page is being fetched
   */
  isFetchingNextPage: boolean;
  /**
   * Whether there are more pages to load
   */
  hasNextPage: boolean;
  /**
   * Error from the query
   */
  error: Error | null;
  /**
   * Function to load the next page
   */
  fetchNextPage: () => void;
  /**
   * Current page number
   */
  currentPage: number;
  /**
   * Total number of items available
   */
  total: number;
}

/**
 * Hook for infinite loading of deals with pagination support.
 * Accumulates results from multiple pages and provides loading states.
 *
 * @param options - Configuration options for the infinite deals query
 * @returns Object containing deals data, loading states, and pagination controls
 *
 * @example
 * ```tsx
 * const {
 *   data: deals,
 *   isLoading,
 *   isFetchingNextPage,
 *   hasNextPage,
 *   fetchNextPage
 * } = useInfiniteDeals({
 *   variables: { categories: ['Electronics'] },
 *   limit: 20
 * });
 * ```
 */
export const useInfiniteDeals = (
  options: UseInfiniteDealsOptions = {},
): UseInfiniteDealsResult => {
  const { variables = {}, limit = 20, enabled = true } = options;
  const { i18n } = useTranslation();

  const [currentPage, setCurrentPage] = useState(1);
  const [allDeals, setAllDeals] = useState<Deal[]>([]);
  const [isFetchingNextPage, setIsFetchingNextPage] = useState(false);

  // Query variables with pagination
  const queryVariables: DealsQueryVariables = useMemo(
    () => ({
      ...variables,
      statuses: [DealStatus.ACTIVE],
      page: currentPage,
      limit,
    }),
    [variables, currentPage, limit],
  );

  // Main query
  const { data, isLoading, error } = useDealsQuery(queryVariables, {
    enabled,
  });

  // Transform and accumulate deals data
  useEffect(() => {
    if (data?.deals?.data) {
      const transformedDeals = data.deals.data
        .map((deal) => {
          const translations = deal?.translations?.find(
            (translation) =>
              translation?.language ===
              LANGUAGE_ABBREVIATION_BY_SHORTNAME[i18n.language],
          );
          const dealTitle = translations?.deal_title;
          const dealDescription = translations?.description;

          return {
            id: deal?.id,
            title: dealTitle,
            imageUrl: deal?.image_url,
            imagePath: deal?.image_path,
            categoryName: deal?.category_name,
            description: dealDescription,
            merchantName: deal?.merchant?.name,
            merchantLogoPath: deal?.merchant?.deals_logo_url,
            featured: deal?.featured,
            discountLabel: translations?.discount_label,
          };
        })
        .filter((deal) => deal.id !== undefined && deal.title !== undefined);

      if (currentPage === 1) {
        // First page - replace all deals
        setAllDeals(transformedDeals);
      } else {
        // Subsequent pages - append to existing deals
        setAllDeals((prev) => [...prev, ...transformedDeals]);
      }
      setIsFetchingNextPage(false);
    }
  }, [data, currentPage, i18n.language]);

  // Pagination info
  const hasNextPage = data?.deals?.has_more_pages ?? false;
  const total = data?.deals?.total ?? 0;

  // Function to fetch next page
  const fetchNextPage = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage && !isLoading) {
      setIsFetchingNextPage(true);
      setCurrentPage((prev) => prev + 1);
    }
  }, [hasNextPage, isFetchingNextPage, isLoading]);

  // Reset when variables change (except page)
  const variablesKey = JSON.stringify(variables);
  useEffect(() => {
    setCurrentPage(1);
    setAllDeals([]);
    setIsFetchingNextPage(false);
  }, [variablesKey, enabled]);

  return {
    data: allDeals,
    isLoading: isLoading && currentPage === 1,
    isFetchingNextPage,
    hasNextPage,
    error: error as Error | null,
    fetchNextPage,
    currentPage,
    total,
  };
};
