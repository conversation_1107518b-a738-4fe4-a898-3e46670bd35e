import { Typography } from '@components/typography';
import { Button } from '@components/ui/button';
import { CarouselItem } from '@components/ui/carousel';
import { useIsMobileView } from '@hooks/system';
import { cn } from '@utils/tailwind';
import { useEffect, useRef, useState } from 'react';
import { useToggle } from 'react-use';

interface DealsCarouselItemProps {
  imgSrc: string;
  title: string;
  ctaLabel: string;
  onCtaClick: () => void;
  isOnlySlide?: boolean;
  shouldLoadOnRedirect?: boolean;
}

export const DealsCarouselItem = ({
  imgSrc,
  title,
  ctaLabel,
  onCtaClick,
  isOnlySlide = false,
  shouldLoadOnRedirect = false,
}: DealsCarouselItemProps) => {
  const isMobileView = useIsMobileView();
  const [isRedirecting, setIsRedirecting] = useToggle(false);
  const [isVisible, setIsVisible] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const itemRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect(); // Stop observing once visible
        }
      },
      {
        threshold: 0.1, // Trigger when 10% of the item is visible
        rootMargin: '50px', // Start loading 50px before the item enters viewport
      },
    );

    if (itemRef.current) {
      observer.observe(itemRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const onButtonClick = () => {
    if (shouldLoadOnRedirect) {
      setIsRedirecting();
      setTimeout(() => {
        setIsRedirecting(false);
      }, 2000);
    }
    onCtaClick();
  };

  return (
    <CarouselItem
      key={title}
      className={cn(
        'mr-1.5 basis-[85%] pl-4',
        isOnlySlide && 'mr-0 basis-full pl-4',
      )}
    >
      <div
        ref={itemRef}
        className="relative flex h-[20rem] w-full flex-col justify-end rounded-2xl overflow-hidden px-6 py-8"
      >
        <div className="absolute inset-0 bg-gray-300 rounded-2xl" />

        {isVisible && (
          <img
            src={imgSrc}
            alt={title}
            onLoad={handleImageLoad}
            className={cn(
              'absolute inset-0 h-full w-full object-cover rounded-2xl transition-opacity duration-300',
              imageLoaded ? 'opacity-100' : 'opacity-0',
            )}
          />
        )}

        <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-black/40 rounded-2xl" />

        <div
          className="absolute inset-0 rounded-2xl"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='3' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%' height='100%' filter='url(%23noise)' opacity='0.45'/%3E%3C/svg%3E")`,
            mixBlendMode: 'overlay',
          }}
        />

        <Typography
          variant={isMobileView ? 'xs' : 'm'}
          className="max-w-[31.75rem] text-white relative z-10"
        >
          {title}
        </Typography>
        <Button
          loading={isRedirecting}
          onClick={onButtonClick}
          size="small"
          variant="white"
          className="relative z-10 mt-6 w-fit"
        >
          {ctaLabel}
        </Button>
      </div>
    </CarouselItem>
  );
};
