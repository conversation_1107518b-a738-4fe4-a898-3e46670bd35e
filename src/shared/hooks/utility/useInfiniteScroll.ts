import { useCallback, useEffect, useRef, useState } from 'react';

export interface UseInfiniteScrollOptions {
  /**
   * Whether to enable the infinite scroll functionality
   */
  enabled?: boolean;
  /**
   * Root margin for the intersection observer
   */
  rootMargin?: string;
  /**
   * Threshold for the intersection observer
   */
  threshold?: number;
  /**
   * Whether there are more items to load
   */
  hasNextPage?: boolean;
  /**
   * Whether a request is currently in progress
   */
  isFetching?: boolean;
}

export interface UseInfiniteScrollResult {
  /**
   * Ref to attach to the trigger element (usually the last item or a loading indicator)
   */
  triggerRef: React.RefObject<HTMLElement>;
  /**
   * Whether the trigger element is currently intersecting
   */
  isIntersecting: boolean;
}

/**
 * A reusable hook for implementing infinite scroll functionality using intersection observer.
 *
 * @param onLoadMore - Callback function to load more items
 * @param options - Configuration options for the infinite scroll
 * @returns Object containing the trigger ref and intersection state
 *
 * @example
 * ```tsx
 * const { triggerRef, isIntersecting } = useInfiniteScroll(
 *   () => fetchNextPage(),
 *   {
 *     enabled: !isLoading,
 *     hasNextPage: hasMore,
 *     isFetching: isFetchingNextPage,
 *     rootMargin: '100px'
 *   }
 * );
 *
 * return (
 *   <div>
 *     {items.map(item => <Item key={item.id} {...item} />)}
 *     {hasMore && <div ref={triggerRef}>Loading...</div>}
 *   </div>
 * );
 * ```
 */
export const useInfiniteScroll = (
  onLoadMore: () => void,
  options: UseInfiniteScrollOptions = {},
): UseInfiniteScrollResult => {
  const {
    enabled = true,
    rootMargin = '100px',
    threshold = 0.1,
    hasNextPage = true,
    isFetching = false,
  } = options;

  const triggerRef = useRef<HTMLElement>(null);
  const [hasTriggered, setHasTriggered] = useState(false);
  const [isIntersecting, setIsIntersecting] = useState(false);

  // Custom intersection observer implementation
  useEffect(() => {
    const element = triggerRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        setIsIntersecting(entry.isIntersecting);
      },
      {
        root: null,
        rootMargin,
        threshold,
      },
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [rootMargin, threshold]);

  // Trigger load more when the element comes into view
  const handleLoadMore = useCallback(() => {
    if (
      enabled &&
      isIntersecting &&
      hasNextPage &&
      !isFetching &&
      !hasTriggered
    ) {
      setHasTriggered(true);
      onLoadMore();

      // Reset the trigger after a short delay to allow for multiple triggers
      setTimeout(() => {
        setHasTriggered(false);
      }, 100);
    }
  }, [
    enabled,
    isIntersecting,
    hasNextPage,
    isFetching,
    hasTriggered,
    onLoadMore,
  ]);

  // Effect to handle the intersection
  useEffect(() => {
    handleLoadMore();
  }, [handleLoadMore]);

  return {
    triggerRef,
    isIntersecting,
  };
};
